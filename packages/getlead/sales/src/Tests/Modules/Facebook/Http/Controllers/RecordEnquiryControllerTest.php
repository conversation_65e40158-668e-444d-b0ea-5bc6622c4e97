<?php

declare(strict_types=1);

namespace Getlead\Sales\Tests\Modules\Facebook\Http\Controllers;

use App\Modules\Facebook\Jobs\RecordEnquiry\ProcessRecordEnquiryRequest;
use App\Modules\Facebook\Models\FbWorkFlow;
use App\Modules\Facebook\Models\LeadStatus;
use App\Modules\Facebook\Models\RecordEnquiryRequest;
use App\Modules\Facebook\Models\RecordEnquiryRequestStatus;
use App\Modules\Facebook\Services\WebhookForwardingService;
use Illuminate\Support\Facades\Bus;
use Tests\TestCase;

final class RecordEnquiryControllerTest extends TestCase
{
    protected const ROUTE = 'meta.record-enquiry-webhook';

    public function setUp(): void
    {
        parent::setUp();

        Bus::fake([ProcessRecordEnquiryRequest::class]);

        $this->mock(WebhookForwardingService::class, function ($mock) {
            $mock->shouldReceive('forwardWebhook')->once()->andReturn();
        });
    }

    /**
     * @test
     */
    public function it_should_ignore_permissions_change_webhook(): void
    {
        $requestPayload = [
            'object' => 'permissions',
            'entry' => [
                [
                    'id' => '3661267804164286',
                    'time' => 1741771303,
                    'uid' => '100001541237202',
                    'changed_fields' => [['pages_read_engagement']],
                ],
            ],
        ];

        $this->postJson(route(self::ROUTE), $requestPayload)
            ->assertAccepted()
            ->assertContent('Webhook ignored');
    }

    /**
     * @test
     */
    public function it_should_process_record_enquiry_request(): void
    {
        $requestPayload = [
            'object' => 'page',
            'entry' => [
                [
                    'id' => '3661267804164286',
                    'time' => 1741771303,
                    'uid' => '100001541237202',
                    'changes' => [
                        [
                            'value' => [
                                'adgroup_id' => '120217417728180026',
                                'ad_id' => '120217417728180026',
                                'created_time' => 1746549701,
                                'leadgen_id' => '675432088440855',
                                'page_id' => '233830886487577',
                                'form_id' => '1148717733555697',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $fbWorkFlow = FbWorkFlow::create([
            'fb_page_id' => $requestPayload['entry'][0]['changes'][0]['value']['page_id'],
            'fb_ad_id' => $requestPayload['entry'][0]['changes'][0]['value']['form_id'],
            'fb_vendor_id' => 1234,
            'page_access_token' => 'access_token',
            'mapped_keys' => [],
            'vendor_id' => 1234,
        ]);

        $this->postJson(route(self::ROUTE), $requestPayload)
            ->assertAccepted()
            ->assertContent('Webhook handled');

        Bus::assertDispatched(ProcessRecordEnquiryRequest::class);

        $this->assertDatabaseHas(RecordEnquiryRequest::class, [
            'page_id' => $requestPayload['entry'][0]['changes'][0]['value']['page_id'],
            'form_id' => $requestPayload['entry'][0]['changes'][0]['value']['form_id'],
            'lead_gen_id' => $requestPayload['entry'][0]['changes'][0]['value']['leadgen_id'],
            'vendor_id' => $fbWorkFlow->vendor_id,
            'status' => RecordEnquiryRequestStatus::Pending,
            'lead_data' => null,
            'lead_status' => LeadStatus::Pending,
        ]);
    }
}
