<?php

declare(strict_types=1);

namespace Getlead\Sales\Tests\Modules\Facebook\Services;

use App\Modules\Facebook\Services\WebhookForwardingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

final class WebhookForwardingServiceTest extends TestCase
{
    private WebhookForwardingService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new WebhookForwardingService();

        Bus::fake();
    }

    /**
     * @test
     */
    public function it_should_skip_forwarding_when_urls_not_configured(): void
    {
        Config::set('services.facebook.webhook_forward_urls', []);

        Log::shouldReceive('info')
            ->once()
            ->with('Facebook webhook forward URLs not configured, skipping forwarding');

        $request = Request::create('/webhook', 'POST', ['test' => 'data']);

        $this->service->forwardWebhook($request);
    }

    /**
     * @test
     */
    public function it_should_dispatch_jobs_for_multiple_urls(): void
    {
        $forwardUrls = [
            'https://external-domain1.com/webhook',
            'https://external-domain2.com/webhook'
        ];
        Config::set('services.facebook.webhook_forward_urls', $forwardUrls);
        Config::set('app.url', 'https://app.getlead.co.uk');

        $requestData = [
            'object' => 'page',
            'entry' => [
                [
                    'id' => '123456789',
                    'time' => 1234567890,
                    'changes' => [
                        [
                            'value' => [
                                'page_id' => '123456789',
                                'form_id' => '987654321',
                                'leadgen_id' => '555666777',
                                'created_time' => '1234567890'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $request = Request::create('/webhook', 'POST', $requestData);
        $request->headers->set('HOST', 'app.getlead.co.uk');

        Log::shouldReceive('info')
            ->once()
            ->with('Dispatching Facebook webhook forwarding jobs', [
                'forward_urls' => $forwardUrls,
                'url_count' => 2,
            ]);

        $this->service->forwardWebhook($request);

        Bus::assertDispatched(\App\Modules\Facebook\Jobs\ForwardWebhookJob::class, 2);
    }

    /**
     * @test
     */
    public function it_should_handle_empty_urls_in_list(): void
    {
        $forwardUrls = [
            'https://external-domain1.com/webhook',
            '', // empty URL
            '   ', // whitespace only
            'https://external-domain2.com/webhook'
        ];
        Config::set('services.facebook.webhook_forward_urls', $forwardUrls);

        $request = Request::create('/webhook', 'POST', ['test' => 'data']);

        Log::shouldReceive('info')
            ->once()
            ->with('Dispatching Facebook webhook forwarding jobs', [
                'forward_urls' => $forwardUrls,
                'url_count' => 4,
            ]);

        $this->service->forwardWebhook($request);

        Bus::assertDispatched(\App\Modules\Facebook\Jobs\ForwardWebhookJob::class, 2);
    }
}
