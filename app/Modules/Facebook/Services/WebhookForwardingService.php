<?php

declare(strict_types=1);

namespace App\Modules\Facebook\Services;

use App\Modules\Facebook\Jobs\ForwardWebhookJob;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

final class WebhookForwardingService
{
    public function forwardWebhook(Request $request): void
    {
        $forwardUrls = config('services.facebook.webhook_forward_urls', []);

        if (empty($forwardUrls)) {
            Log::info('Facebook webhook forward URLs not configured, skipping forwarding');
            return;
        }

        $headers = [
            'X-Forwarded-From' => config('app.url'),
            'X-Original-Host' => $request->getHost(),
        ];

        $webhookData = $request->all();

        Log::info('Dispatching Facebook webhook forwarding jobs', [
            'forward_urls' => $forwardUrls,
            'url_count' => count($forwardUrls),
        ]);

        foreach ($forwardUrls as $url) {
            $url = trim($url);
            if (!empty($url)) {
                ForwardWebhookJob::dispatch($url, $webhookData, $headers);
            }
        }
    }
}
